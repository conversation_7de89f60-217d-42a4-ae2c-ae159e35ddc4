import { css, cx } from '@emotion/css'
import { Bookmark, isEmpty } from '../util/bookmark'
import { copyOf } from '../util/bean'
import { Setter } from '../util/type'
import { ReactSortable } from 'react-sortablejs'
import { MdClose } from 'react-icons/md'

export default function UserBookmarks({
  editing,
  bookmarks,
  onSort,
}: {
  editing: boolean
  bookmarks: Bookmark[]
  onSort: Setter<Bookmark[]>
}) {
  return editing ? <SortBookmarks bookmarks={bookmarks} onChange={onSort} /> : <NormalBookmarks bookmarks={bookmarks} />
}

function NormalBookmarks({ bookmarks }: { bookmarks: Bookmark[] }) {
  return (
    <div className={Style.container}>
      {bookmarks.map(bookmark => (
        <NormalBookmark key={bookmark.id} bookmark={bookmark} />
      ))}
    </div>
  )
}

function NormalBookmark({ bookmark }: { bookmark: Bookmark }) {
  return isEmpty(bookmark) ? (
    <div className={Style.col(bookmark)}>
      <span>{bookmark.name}</span>
    </div>
  ) : (
    <a href={bookmark.url} className={Style.col(bookmark)}>
      <img src={bookmark.icon} draggable={false} />
      <span>{bookmark.name}</span>
    </a>
  )
}

function SortBookmarks({ bookmarks, onChange }: { bookmarks: Bookmark[]; onChange: Setter<Bookmark[]> }) {
  return (
    <ReactSortable className={Style.container} list={bookmarks} setList={onChange} animation={200}>
      {bookmarks.map((bookmark, i) => (
        <SortBookmark
          key={bookmark.id}
          bookmark={bookmark}
          onRemove={() => {
            onChange(prev => {
              const newBookmarks = copyOf(prev)
              newBookmarks.splice(i, 1)
              return newBookmarks
            })
          }}
        />
      ))}
    </ReactSortable>
  )
}

function SortBookmark({ bookmark, onRemove }: { bookmark: Bookmark; onRemove: () => void }) {
  return isEmpty(bookmark) ? (
    <div className={cx(Style.col(bookmark), Style.sortCol)}>
      <button className={Style.closer} onClick={onRemove}>
        <MdClose color="rgba(0, 0, 0, 0.4)" />
      </button>

      <span>{bookmark.name}</span>
    </div>
  ) : (
    <div className={cx(Style.col(bookmark), Style.sortCol)}>
      <button className={Style.closer} onClick={onRemove}>
        <MdClose color="rgba(0, 0, 0, 0.4)" />
      </button>

      <img src={bookmark.icon} draggable={false} />

      <span>{bookmark.name}</span>
    </div>
  )
}

const Style = {
  container: css({
    margin: '0px -4px',
    padding: '4px 0 0 0',
    maxHeight: 'calc(100% - 152px)',
    overflow:'visible',
    textAlign: 'center',
  }),
  col: (bookmark: Bookmark) =>
    css({
      display: 'inline-block',
      margin: '4px',
      width: isEmpty(bookmark) ? bookmark.icon : 'auto',
      border: '1px solid rgba(40, 50, 60, 0.2)',
      borderRadius: '15px',
      backgroundColor: 'rgba(244, 245, 246)',
      lineHeight: '28px',
      color: 'inherit',
      textDecoration: 'none',
      cursor: isEmpty(bookmark) ? 'default' : 'pointer',
      opacity: isEmpty(bookmark) ? 0 : 1,
      transition: 'background-color 0.1s ease',
      ':hover': {
        backgroundColor: 'rgba(234, 235, 236)',
      },
      ':active': {
        backgroundColor: 'rgba(214, 215, 216)',
      },
      '> svg': {
        width: '16px',
        height: '16px',
        margin: '6px',
        verticalAlign: 'top',
      },
      '> img': {
        width: '20px',
        height: '20px',
        margin: '4px -4px 4px 8px',
        verticalAlign: 'top',
        borderRadius: '2px',
      },
      '> span': {
        opacity: isEmpty(bookmark) ? 0 : 1,
        verticalAlign: 'top',
        display: 'inline-block',
        margin: '0 8px',
        width: 'calc(100% - 40px)',
        maxWidth: '256px',
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
      },
    }),
  sortCol: css({
    opacity: 1,
    position: 'relative',
    cursor: 'grab',
    ':hover': {
      backgroundColor: 'rgba(244, 245, 246)',
    },
    ':active': {
      backgroundColor: 'rgba(244, 245, 246)',
    },
  }),
  closer: css({
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 0,
    position: 'absolute',
    right: '-8px',
    top: '-8px',
    width: '20px',
    height: '20px',
    color: 'rgba(0, 0, 0, 0.4)',
    backgroundColor: 'rgba(250, 120, 120)',
    border: '1px solid rgba(0, 0, 0, 0.2)',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    borderRadius: '10px',
    cursor: 'pointer',
    transition: 'background-color 0.1s ease',
    ':hover': {
      backgroundColor: 'rgba(240, 110, 110)',
    },
    ':active': {
      backgroundColor: 'rgba(220, 90, 90)',
    },
  }),
}
