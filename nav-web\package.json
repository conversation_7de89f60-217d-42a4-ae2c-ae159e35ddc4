{"name": "nav", "version": "0.0.6", "scripts": {"dev": "vite", "build": "tsc && vite build", "serve": "vite preview", "ncu": "ncu -u"}, "dependencies": {"@emotion/css": "^11.13.5", "@emotion/react": "^11.14.0", "axios": "^1.10.0", "jsonp": "^0.2.1", "nanoid": "^5.1.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-sortablejs": "^6.1.4", "sortablejs": "^1.15.6"}, "devDependencies": {"@types/jsonp": "^0.2.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-react": "^4.5.2", "npm-check-updates": "^18.0.1", "typescript": "^5.8.3", "vite": "^6.3.5"}}